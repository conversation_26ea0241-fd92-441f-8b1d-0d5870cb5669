//! Raydium CLMM 类型定义
//!
//! 定义 Raydium CLMM 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use shared::{EchoesError, Result};

/// Q64.64 定点数常量
pub mod math_constants {
    /// Q64.64 格式的 1.0
    pub const Q64_64_ONE: u128 = 1u128 << 64;
    /// 最大 sqrt_price (对应 tick = 443636)
    pub const MAX_SQRT_PRICE: u128 = 79226673515401279992447579055;
    /// 最小 sqrt_price (对应 tick = -443636)
    pub const MIN_SQRT_PRICE: u128 = 4295128739;
    /// 最大 tick
    pub const MAX_TICK: i32 = 443636;
    /// 最小 tick
    pub const MIN_TICK: i32 = -443636;
    /// tick 基数 1.0001
    pub const TICK_BASE: f64 = 1.0001;
}

/// 精确的 Q64.64 定点数学运算
pub mod fixed_point_math {
    use super::math_constants::*;
    use shared::{EchoesError, Result};

    /// 将 tick 转换为 sqrt_price (Q64.64 格式)
    /// 使用精确的数学计算: sqrt_price = 1.0001^(tick/2) * 2^64
    pub fn tick_to_sqrt_price(tick: i32) -> Result<u128> {
        if tick < MIN_TICK || tick > MAX_TICK {
            return Err(EchoesError::InvalidInput(format!("Tick {} out of bounds", tick)));
        }

        // 特殊情况：tick = 0 对应 sqrt_price = 1.0
        if tick == 0 {
            return Ok(Q64_64_ONE);
        }

        // 使用高精度浮点数计算，然后转换为定点数
        // sqrt_price = 1.0001^(tick/2)
        let tick_half = tick as f64 / 2.0;
        let sqrt_price_float = TICK_BASE.powf(tick_half);

        // 转换为 Q64.64 格式
        let sqrt_price_q64 = (sqrt_price_float * (1u128 << 64) as f64) as u128;

        // 确保结果在有效范围内
        let result = sqrt_price_q64.max(MIN_SQRT_PRICE).min(MAX_SQRT_PRICE);
        Ok(result)
    }

    /// 将 sqrt_price (Q64.64) 转换为 tick
    pub fn sqrt_price_to_tick(sqrt_price: u128) -> Result<i32> {
        if sqrt_price < MIN_SQRT_PRICE || sqrt_price > MAX_SQRT_PRICE {
            return Err(EchoesError::InvalidInput(format!("sqrt_price {} out of bounds", sqrt_price)));
        }

        // 使用二分查找来找到对应的 tick
        let mut low = MIN_TICK;
        let mut high = MAX_TICK;

        while low <= high {
            let mid = (low + high) / 2;
            let mid_sqrt_price = tick_to_sqrt_price(mid)?;

            if mid_sqrt_price == sqrt_price {
                return Ok(mid);
            } else if mid_sqrt_price < sqrt_price {
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }

        Ok(high)
    }

    /// Q64.64 格式的乘法
    pub fn mul_q64_64(a: u128, b: u128) -> Result<u128> {
        // 高质量 Q64.64 乘法，避免溢出
        // (a * b) >> 64
        let a_hi = a >> 64;
        let a_lo = a & ((1u128 << 64) - 1);
        let b_hi = b >> 64;
        let b_lo = b & ((1u128 << 64) - 1);

        // 执行部分乘法
        let lo_lo = a_lo * b_lo;
        let lo_hi = a_lo * b_hi;
        let hi_lo = a_hi * b_lo;
        let hi_hi = a_hi * b_hi;

        // 组合结果，右移64位
        let result = (lo_lo >> 64) + lo_hi + hi_lo + (hi_hi << 64);
        Ok(result)
    }

    /// Q64.64 格式的除法
    pub fn div_q64_64(a: u128, b: u128) -> Result<u128> {
        if b == 0 {
            return Err(EchoesError::InvalidInput("Division by zero".to_string()));
        }

        // 使用高精度除法避免溢出
        // result = (a << 64) / b
        let a_hi = a >> 64;
        let a_lo = a & ((1u128 << 64) - 1);

        // 分别计算高位和低位部分
        let result_hi = (a_hi << 64) / b;
        let remainder_hi = (a_hi << 64) % b;
        let result_lo = ((remainder_hi << 64) + (a_lo << 64)) / b;

        let result = result_hi + result_lo;

        Ok(result)
    }
}

/// AMM 数学计算模块
pub mod amm_math {
    use super::fixed_point_math::*;
    use super::math_constants::*;
    use shared::{EchoesError, Result};

    /// 计算给定输入量的输出量 (基于实际输入量的简化计算)
    pub fn get_amount_out(
        amount_in: u128,
        sqrt_price_current: u128,
        sqrt_price_target: u128,
        liquidity: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if liquidity == 0 || amount_in == 0 {
            return Ok(0);
        }

        // 简化但正确的价格计算
        // sqrt_price 是 Q64.64 格式，实际价格 = (sqrt_price / 2^64)^2
        let sqrt_price_f64 = sqrt_price_current as f64 / (Q64_64_ONE as f64);
        let price = sqrt_price_f64 * sqrt_price_f64;

        if zero_for_one {
            // Token0 -> Token1: SOL -> USDT
            // 输入：lamports (1 lamport = 10^-9 SOL)
            // 输出：micro USDT (1 micro USDT = 10^-6 USDT)
            // 注意：get_current_price() 已经调整了小数位差异，返回的价格单位是 micro USDT / lamport
            // 因此直接相乘即可：amount_out = amount_in_lamports * price_micro_usdt_per_lamport
            let amount_out_f64 = (amount_in as f64) * price;
            Ok(amount_out_f64 as u128)
        } else {
            // Token1 -> Token0: USDT -> SOL
            // 输入：micro USDT (1 micro USDT = 10^-6 USDT)
            // 输出：lamports (1 lamport = 10^-9 SOL)
            // 注意：get_current_price() 返回的价格单位是 micro USDT / lamport
            // 因此：amount_out = amount_in_micro_usdt / price_micro_usdt_per_lamport
            let amount_out_f64 = (amount_in as f64) / price;
            Ok(amount_out_f64 as u128)
        }
    }

    /// 计算给定输出量需要的输入量
    pub fn get_amount_in(
        amount_out: u128,
        sqrt_price_current: u128,
        _sqrt_price_target: u128,
        _liquidity: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if amount_out == 0 {
            return Ok(0);
        }

        // 简化但正确的价格计算 - 与 get_amount_out 保持一致
        // sqrt_price 是 Q64.64 格式，实际价格 = (sqrt_price / 2^64)^2
        let sqrt_price_f64 = sqrt_price_current as f64 / (Q64_64_ONE as f64);
        let price = sqrt_price_f64 * sqrt_price_f64;

        if zero_for_one {
            // Token0 -> Token1: SOL -> USDT
            // 输出：micro USDT，输入：lamports
            // 注意：get_current_price() 返回的价格单位是 micro USDT / lamport
            // 因此：amount_in = amount_out_micro_usdt / price_micro_usdt_per_lamport
            let amount_in_f64 = (amount_out as f64) / price;
            Ok(amount_in_f64 as u128)
        } else {
            // Token1 -> Token0: USDT -> SOL
            // 输出：lamports，输入：micro USDT
            // 注意：get_current_price() 返回的价格单位是 micro USDT / lamport
            // 因此：amount_in = amount_out_lamports * price_micro_usdt_per_lamport
            let amount_in_f64 = (amount_out as f64) * price;
            Ok(amount_in_f64 as u128)
        }
    }
}

/// 位图操作模块 - 用于高效的 tick 查找
pub mod bitmap_utils {

    /// 在位图中查找下一个初始化的 tick (安全版本)
    pub fn next_initialized_tick_within_one_word(
        bitmap: u64,
        tick: i32,
        tick_spacing: i32,
        left_to_right: bool,
    ) -> Option<(i32, bool)> {
        if bitmap == 0 {
            return None;
        }

        // 简化的实现，避免复杂的位运算
        let base_tick = (tick / tick_spacing) * tick_spacing;

        if left_to_right {
            // 向右查找 (tick 增加)
            for i in 0..64 {
                let bit_mask = 1u64 << i;
                if bitmap & bit_mask != 0 {
                    let candidate_tick = base_tick + i * tick_spacing;
                    if candidate_tick > tick {
                        return Some((candidate_tick, true));
                    }
                }
            }
        } else {
            // 向左查找 (tick 减少)
            for i in (0..64).rev() {
                let bit_mask = 1u64 << i;
                if bitmap & bit_mask != 0 {
                    let candidate_tick = base_tick + i * tick_spacing;
                    if candidate_tick < tick {
                        return Some((candidate_tick, true));
                    }
                }
            }
        }

        None
    }

    /// 计算 tick 对应的位图字索引和位位置
    pub fn position(tick: i32, tick_spacing: i32) -> (i16, u8) {
        let compressed = tick / tick_spacing;
        let word_pos = compressed >> 8; // 除以 256
        let bit_pos = (compressed % 256) as u8;
        (word_pos as i16, bit_pos)
    }

    /// 翻转位图中的位
    pub fn flip_tick(bitmap: &mut [u64; 16], tick: i32, tick_spacing: i32) {
        let (word_pos, bit_pos) = position(tick, tick_spacing);
        let word_index = (word_pos + 8) as usize; // 偏移到数组中心

        if word_index < 16 {
            let mask = 1u64 << (bit_pos % 64);
            bitmap[word_index] ^= mask;
        }
    }

    /// 检查 tick 是否已初始化
    pub fn is_initialized(bitmap: &[u64; 16], tick: i32, tick_spacing: i32) -> bool {
        let (word_pos, bit_pos) = position(tick, tick_spacing);
        let word_index = (word_pos + 8) as usize;

        if word_index < 16 {
            let mask = 1u64 << (bit_pos % 64);
            bitmap[word_index] & mask != 0
        } else {
            false
        }
    }
}

/// 费用计算模块
pub mod fee_math {
    /// 计算交换费用
    pub fn compute_fee_amount(amount: u128, fee_rate: u32) -> u64 {
        // fee_rate 以 basis points 为单位 (例如 3000 = 0.3%)
        ((amount * fee_rate as u128) / 1_000_000) as u64
    }

    /// 从输入量中扣除费用后的净输入量
    pub fn amount_after_fee(amount_in: u128, fee_rate: u32) -> u128 {
        let fee = compute_fee_amount(amount_in, fee_rate) as u128;
        amount_in.saturating_sub(fee)
    }
}

/// 奖励信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RewardInfo {
    /// 奖励状态
    pub reward_state: u8,
    /// 开始时间
    pub open_time: u64,
    /// 结束时间
    pub end_time: u64,
    /// 最后更新时间
    pub last_update_time: u64,
    /// 每秒发放量 (Q64.64格式)
    pub emissions_per_second_x64: u128,
    /// 总发放量
    pub reward_total_emissioned: u64,
    /// 已领取量
    pub reward_claimed: u64,
    /// 奖励代币铸币地址
    pub token_mint: Pubkey,
    /// 奖励代币库地址
    pub token_vault: Pubkey,
    /// 权限地址
    pub authority: Pubkey,
    /// 全局奖励增长 (Q64.64格式)
    pub reward_growth_global_x64: u128,
}

/// Tick信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickInfo {
    /// Tick值
    pub tick: i32,
    /// 净流动性变化
    pub liquidity_net: i128,
    /// 总流动性
    pub liquidity_gross: u128,
    /// 费用增长外部 Token 0 (Q64.64格式)
    pub fee_growth_outside_0_x64: u128,
    /// 费用增长外部 Token 1 (Q64.64格式)
    pub fee_growth_outside_1_x64: u128,
    /// 奖励增长外部 (Q64.64格式) - 支持3个奖励
    pub reward_growths_outside_x64: [u128; 3],
}

/// Tick数组状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickArrayState {
    /// 所属池ID
    pub pool_id: Pubkey,
    /// 起始tick索引
    pub start_tick_index: i32,
    /// Tick信息数组 (88个tick)
    pub ticks: Vec<TickInfo>,
    /// 已初始化的tick数量
    pub initialized_tick_count: u16,
    /// 最近周期
    pub recent_epoch: u64,
}

/// Tick数组位图扩展
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickArrayBitmapExtension {
    /// 所属池ID
    pub pool_id: Pubkey,
    /// 正向tick数组位图 [14][8]
    pub positive_tick_array_bitmap: [[u64; 8]; 14],
    /// 负向tick数组位图 [14][8]
    pub negative_tick_array_bitmap: [[u64; 8]; 14],
}

/// 交换配置
#[derive(Debug, Clone)]
pub struct SwapConfig {
    /// 费用率 (basis points, 例如 3000 = 0.3%)
    pub fee_rate: u32,
    /// 协议费用率 (basis points)
    pub protocol_fee_rate: u32,
    /// 最大滑点保护 (basis points)
    pub max_slippage_bps: u32,
    /// 是否启用精确输入模式
    pub exact_in: bool,
}

impl Default for SwapConfig {
    fn default() -> Self {
        Self {
            fee_rate: 3000, // 0.3%
            protocol_fee_rate: 0,
            max_slippage_bps: 100, // 1%
            exact_in: true,
        }
    }
}

/// 增强的交换结果
#[derive(Debug, Clone)]
pub struct SwapResult {
    pub amount_in: u64,
    pub amount_out: u64,
    pub fee_amount: u64,
    pub protocol_fee_amount: u64,
    pub sqrt_price_after: u128,
    pub tick_after: i32,
    pub liquidity_after: u128,
    pub price_impact_bps: u32,
}

/// 交换步骤计算结果
#[derive(Debug, Clone)]
pub struct SwapStepComputation {
    pub sqrt_price_next: u128,
    pub amount_in: u128,
    pub amount_out: u128,
    pub fee_amount: u64,
}

/// 池统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    pub total_liquidity: u128,
    pub current_tick: i32,
    pub current_price: f64,
    pub total_ticks: usize,
    pub initialized_ticks: usize,
    pub tick_arrays_count: usize,
    pub tvl_token_0: f64,
    pub tvl_token_1: f64,
}

/// 高级交换计算模块
pub mod swap_math {
    use super::*;
    use super::amm_math::*;
    use super::fee_math::*;
    use super::fixed_point_math::*;

    /// 计算下一个 sqrt_price (根据输入量)
    pub fn get_next_sqrt_price_from_input(
        sqrt_price: u128,
        liquidity: u128,
        amount_in: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if liquidity == 0 {
            return Ok(sqrt_price);
        }

        if zero_for_one {
            // Token0 -> Token1: sqrt_price 下降
            // √P_new = L * √P / (L + Δx * √P)
            let numerator = mul_q64_64(liquidity, sqrt_price)?;
            let delta_product = mul_q64_64(amount_in, sqrt_price)?;
            let denominator = liquidity + delta_product;
            div_q64_64(numerator, denominator)
        } else {
            // Token1 -> Token0: sqrt_price 上升
            // √P_new = √P + Δy / L
            let price_delta = div_q64_64(amount_in, liquidity)?;
            Ok(sqrt_price + price_delta)
        }
    }

    /// 计算下一个 sqrt_price (从输出量)
    pub fn get_next_sqrt_price_from_output(
        sqrt_price: u128,
        liquidity: u128,
        amount_out: u128,
        zero_for_one: bool,
    ) -> Result<u128> {
        if liquidity == 0 {
            return Ok(sqrt_price);
        }

        if zero_for_one {
            // Token0 -> Token1: sqrt_price 下降
            // √P_new = L * √P / (L + Δy * √P)
            let numerator = mul_q64_64(liquidity, sqrt_price)?;
            let denominator_delta = mul_q64_64(amount_out, sqrt_price)?;
            let denominator = liquidity + denominator_delta;
            div_q64_64(numerator, denominator)
        } else {
            // Token1 -> Token0: sqrt_price 上升
            // √P_new = √P + Δx / L
            let price_delta = div_q64_64(amount_out, liquidity)?;
            Ok(sqrt_price + price_delta)
        }
    }

    /// 计算单步交换 (生产级实现)
    pub fn compute_swap_step(
        sqrt_price_current: u128,
        sqrt_price_target: u128,
        liquidity: u128,
        amount_remaining: u128,
        fee_rate: u32,
        zero_for_one: bool,
        exact_in: bool,
    ) -> Result<SwapStepComputation> {
        if liquidity == 0 {
            return Ok(SwapStepComputation {
                sqrt_price_next: sqrt_price_current,
                amount_in: 0,
                amount_out: 0,
                fee_amount: 0,
            });
        }

        let sqrt_price_next = if exact_in {
            // 精确输入模式
            let amount_in_after_fee = amount_after_fee(amount_remaining, fee_rate);
            get_next_sqrt_price_from_input(
                sqrt_price_current,
                liquidity,
                amount_in_after_fee,
                zero_for_one,
            )?
        } else {
            // 精确输出模式
            get_next_sqrt_price_from_output(
                sqrt_price_current,
                liquidity,
                amount_remaining,
                zero_for_one,
            )?
        };

        // 确保不超过目标价格
        let sqrt_price_next = if zero_for_one {
            sqrt_price_next.max(sqrt_price_target)
        } else {
            sqrt_price_next.min(sqrt_price_target)
        };

        // 计算实际的输入和输出量
        let (amount_in, amount_out) = if sqrt_price_next == sqrt_price_target {
            // 达到目标价格
            if exact_in {
                let amount_out = get_amount_out(
                    amount_remaining,
                    sqrt_price_current,
                    sqrt_price_next,
                    liquidity,
                    zero_for_one,
                )?;
                (amount_remaining, amount_out)
            } else {
                let amount_in = get_amount_in(
                    amount_remaining,
                    sqrt_price_current,
                    sqrt_price_next,
                    liquidity,
                    zero_for_one,
                )?;
                (amount_in, amount_remaining)
            }
        } else {
            // 未达到目标价格，消耗所有剩余量
            if exact_in {
                let amount_out = get_amount_out(
                    amount_remaining,
                    sqrt_price_current,
                    sqrt_price_next,
                    liquidity,
                    zero_for_one,
                )?;
                (amount_remaining, amount_out)
            } else {
                let amount_in = get_amount_in(
                    amount_remaining,
                    sqrt_price_current,
                    sqrt_price_next,
                    liquidity,
                    zero_for_one,
                )?;
                (amount_in, amount_remaining)
            }
        };

        let fee_amount = compute_fee_amount(amount_in, fee_rate);

        Ok(SwapStepComputation {
            sqrt_price_next,
            amount_in,
            amount_out,
            fee_amount,
        })
    }
}

/// Raydium CLMM 位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RaydiumClmmPositionInfo {
    /// 位置 NFT 铸币地址
    pub nft_mint: Pubkey,
    /// 池 ID
    pub pool_id: Pubkey,
    /// 下边界 Tick
    pub tick_lower_index: i32,
    /// 上边界 Tick
    pub tick_upper_index: i32,
    /// 流动性
    pub liquidity: u128,
    /// 费用增长内部 X
    pub fee_growth_inside_last_x64: u128,
    /// 费用增长内部 Y
    pub fee_growth_inside_last_y64: u128,
    /// 代币欠费 X
    pub token_fees_owed_x: u64,
    /// 代币欠费 Y
    pub token_fees_owed_y: u64,
    /// 奖励增长内部
    pub reward_growth_inside_last_x64: [u128; 3],
    /// 奖励欠费
    pub reward_amounts_owed: [u64; 3],
}

/// Raydium CLMM 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RaydiumClmmError {
    /// 无效的 Tick 范围
    InvalidTickRange,
    /// 无效的流动性
    InvalidLiquidity,
    /// 无效的价格
    InvalidPrice,
    /// Tick 不存在
    TickNotFound,
    /// 位置不存在
    PositionNotFound,
    /// 计算溢出
    CalculationOverflow,
}

impl std::fmt::Display for RaydiumClmmError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RaydiumClmmError::InvalidTickRange => write!(f, "Invalid tick range"),
            RaydiumClmmError::InvalidLiquidity => write!(f, "Invalid liquidity"),
            RaydiumClmmError::InvalidPrice => write!(f, "Invalid price"),
            RaydiumClmmError::TickNotFound => write!(f, "Tick not found"),
            RaydiumClmmError::PositionNotFound => write!(f, "Position not found"),
            RaydiumClmmError::CalculationOverflow => write!(f, "Calculation overflow"),
        }
    }
}

impl std::error::Error for RaydiumClmmError {}

/// Raydium CLMM 结果类型
pub type RaydiumClmmResult<T> = std::result::Result<T, RaydiumClmmError>;
