//! Raydium CLMM 池管理器实现
//!
//! 实现 Raydium CLMM 特定的池管理逻辑

use super::types::*;
use super::pool::RaydiumClmmPoolState;
use solana_sdk::pubkey::Pubkey;
use std::collections::BTreeMap;
use shared::{EchoesError, Result};

/// 交换状态
#[derive(Debug, Clone)]
struct SwapState {
    amount_specified_remaining: u128,
    amount_calculated: u128,
    sqrt_price: u128,
    tick: i32,
    liquidity: u128,
    fee_amount: u64,
    protocol_fee_amount: u64,
}

/// 交换步骤结果
#[derive(Debug, <PERSON>lone)]
struct SwapStepResult {
    sqrt_price_next: u128,
    amount_in: u128,
    amount_out: u128,
    fee_amount: u64,
    tick_next: i32,
}

/// Raydium CLMM 池管理器
#[derive(Debug)]
pub struct RaydiumClmmPoolManager {
    /// 池状态
    pub pool_state: RaydiumClmmPoolState,
    /// Tick数组缓存 start_tick_index -> TickArrayState
    tick_arrays: BTreeMap<i32, TickArrayState>,
    /// 位图扩展
    bitmap_extension: Option<TickArrayBitmapExtension>,
}

impl RaydiumClmmPoolManager {
    /// 创建新的池管理器
    pub fn new(pool_state: RaydiumClmmPoolState) -> Self {
        Self {
            pool_state,
            tick_arrays: BTreeMap::new(),
            bitmap_extension: None,
        }
    }

    /// 更新池状态
    pub fn update_pool_state(&mut self, new_state: RaydiumClmmPoolState) {
        self.pool_state = new_state;
    }

    /// 添加或更新tick数组
    pub fn upsert_tick_array(&mut self, tick_array: TickArrayState) {
        self.tick_arrays.insert(tick_array.start_tick_index, tick_array);
    }

    /// 设置位图扩展
    pub fn set_bitmap_extension(&mut self, bitmap: TickArrayBitmapExtension) {
        self.bitmap_extension = Some(bitmap);
    }

    /// 获取当前价格 (Token1/Token0)
    pub fn get_current_price(&self) -> f64 {
        // sqrt_price_x64 转换为实际价格
        // price = (sqrt_price_x64 / 2^64)^2
        let sqrt_price = self.pool_state.sqrt_price_x64 as f64 / (1u128 << 64) as f64;
        let price = sqrt_price * sqrt_price;

        // 调整小数位差异
        let decimals_diff = self.pool_state.mint_decimals_0 as i32 - self.pool_state.mint_decimals_1 as i32;
        price * 10f64.powi(decimals_diff)
    }

    /// 获取指定tick的信息
    pub fn get_tick_info(&self, tick: i32) -> Option<&TickInfo> {
        // 计算包含此tick的tick数组起始索引
        let tick_array_start = self.get_tick_array_start_index(tick);

        if let Some(tick_array) = self.tick_arrays.get(&tick_array_start) {
            // 在tick数组中查找具体的tick
            tick_array.ticks.iter().find(|t| t.tick == tick)
        } else {
            None
        }
    }

    /// 计算tick数组的起始索引
    fn get_tick_array_start_index(&self, tick: i32) -> i32 {
        let tick_spacing = self.pool_state.tick_spacing as i32;
        let ticks_per_array = 60; // Raydium CLMM每个数组包含60个tick
        let array_size = tick_spacing * ticks_per_array;

        if tick >= 0 {
            (tick / array_size) * array_size
        } else {
            ((tick + 1) / array_size - 1) * array_size
        }
    }

    /// 估算交换输出 (生产级实现)
    pub fn estimate_swap_output(
        &self,
        input_amount: u64,
        zero_for_one: bool, // true: Token0 -> Token1, false: Token1 -> Token0
        sqrt_price_limit: Option<u128>,
    ) -> Result<SwapResult> {
        self.estimate_swap_output_with_config(
            input_amount,
            zero_for_one,
            sqrt_price_limit,
            &SwapConfig::default(),
        )
    }

    /// 带配置的交换输出估算
    pub fn estimate_swap_output_with_config(
        &self,
        input_amount: u64,
        zero_for_one: bool,
        sqrt_price_limit: Option<u128>,
        config: &SwapConfig,
    ) -> Result<SwapResult> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput("Input amount cannot be zero".to_string()));
        }

        // 验证价格限制
        if let Some(limit) = sqrt_price_limit {
            if (zero_for_one && limit >= self.pool_state.sqrt_price_x64) ||
               (!zero_for_one && limit <= self.pool_state.sqrt_price_x64) {
                return Err(EchoesError::InvalidInput("Invalid price limit".to_string()));
            }
        }

        let initial_sqrt_price = self.pool_state.sqrt_price_x64;
        let mut state = SwapState {
            amount_specified_remaining: input_amount as u128,
            amount_calculated: 0,
            sqrt_price: initial_sqrt_price,
            tick: self.pool_state.tick_current,
            liquidity: self.pool_state.liquidity,
            fee_amount: 0,
            protocol_fee_amount: 0,
        };

        // 设置价格限制
        let sqrt_price_limit = sqrt_price_limit.unwrap_or(
            if zero_for_one {
                math_constants::MIN_SQRT_PRICE + 1
            } else {
                math_constants::MAX_SQRT_PRICE - 1
            }
        );

        // 主交换循环
        while state.amount_specified_remaining != 0 && state.sqrt_price != sqrt_price_limit {
            let step = self.compute_swap_step_advanced(
                &mut state,
                sqrt_price_limit,
                zero_for_one,
                config,
            )?;

            // 更新状态
            state.sqrt_price = step.sqrt_price_next;
            state.amount_specified_remaining = state.amount_specified_remaining.saturating_sub(step.amount_in);
            state.amount_calculated += step.amount_out;
            state.fee_amount += step.fee_amount;

            // 如果跨越了tick边界，更新流动性
            if step.sqrt_price_next == fixed_point_math::tick_to_sqrt_price(step.tick_next)? {
                if let Some(tick_info) = self.get_tick_info(step.tick_next) {
                    let liquidity_net = if zero_for_one {
                        -tick_info.liquidity_net
                    } else {
                        tick_info.liquidity_net
                    };

                    state.liquidity = if liquidity_net < 0 {
                        state.liquidity.saturating_sub((-liquidity_net) as u128)
                    } else {
                        state.liquidity.saturating_add(liquidity_net as u128)
                    };
                }

                state.tick = if zero_for_one {
                    step.tick_next - 1
                } else {
                    step.tick_next
                };
            } else {
                state.tick = fixed_point_math::sqrt_price_to_tick(step.sqrt_price_next)?;
            }
        }

        // 计算价格影响
        let price_impact_bps = self.calculate_price_impact(
            initial_sqrt_price,
            state.sqrt_price,
        );

        Ok(SwapResult {
            amount_in: (input_amount as u128 - state.amount_specified_remaining) as u64,
            amount_out: state.amount_calculated as u64,
            fee_amount: state.fee_amount,
            protocol_fee_amount: state.protocol_fee_amount,
            sqrt_price_after: state.sqrt_price,
            tick_after: state.tick,
            liquidity_after: state.liquidity,
            price_impact_bps,
        })
    }

    /// 高级交换步骤计算
    fn compute_swap_step_advanced(
        &self,
        state: &SwapState,
        sqrt_price_limit: u128,
        zero_for_one: bool,
        config: &SwapConfig,
    ) -> Result<SwapStepResult> {
        // 获取下一个初始化的tick
        let next_tick = self.get_next_initialized_tick_optimized(state.tick, zero_for_one)?;

        let sqrt_price_next_tick = if let Some(tick) = next_tick {
            fixed_point_math::tick_to_sqrt_price(tick)?
        } else {
            sqrt_price_limit
        };

        // 确定目标价格
        let sqrt_price_target = if zero_for_one {
            sqrt_price_next_tick.max(sqrt_price_limit)
        } else {
            sqrt_price_next_tick.min(sqrt_price_limit)
        };

        // 使用高精度数学计算交换
        let swap_computation = swap_math::compute_swap_step(
            state.sqrt_price,
            sqrt_price_target,
            state.liquidity,
            state.amount_specified_remaining,
            config.fee_rate,
            zero_for_one,
            config.exact_in,
        )?;

        Ok(SwapStepResult {
            sqrt_price_next: swap_computation.sqrt_price_next,
            amount_in: swap_computation.amount_in,
            amount_out: swap_computation.amount_out,
            fee_amount: swap_computation.fee_amount,
            tick_next: next_tick.unwrap_or(state.tick),
        })
    }

    /// 优化的下一个初始化tick查找
    fn get_next_initialized_tick_optimized(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        // 首先尝试使用位图快速查找
        if let Some(next_tick) = self.find_next_tick_with_bitmap(current_tick, zero_for_one)? {
            return Ok(Some(next_tick));
        }

        // 回退到线性搜索（用于调试或位图不可用时）
        self.find_next_tick_linear(current_tick, zero_for_one)
    }

    /// 使用位图查找下一个初始化的tick
    fn find_next_tick_with_bitmap(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        let tick_spacing = self.pool_state.tick_spacing as i32;

        // 使用主位图
        if let Some(tick) = self.search_in_main_bitmap(current_tick, zero_for_one, tick_spacing) {
            return Ok(Some(tick));
        }

        // 如果有扩展位图，也搜索扩展位图
        if let Some(bitmap_ext) = &self.bitmap_extension {
            if let Some(tick) = self.search_in_extended_bitmap(
                current_tick,
                zero_for_one,
                tick_spacing,
                bitmap_ext,
            ) {
                return Ok(Some(tick));
            }
        }

        Ok(None)
    }

    /// 在主位图中搜索
    fn search_in_main_bitmap(
        &self,
        current_tick: i32,
        zero_for_one: bool,
        tick_spacing: i32,
    ) -> Option<i32> {
        let bitmap = &self.pool_state.tick_array_bitmap;

        // 计算当前tick在位图中的位置
        let (word_pos, _bit_pos) = bitmap_utils::position(current_tick, tick_spacing);
        let word_index = (word_pos + 8) as usize; // 偏移到数组中心

        if word_index >= 16 {
            return None;
        }

        // 在当前字中查找
        if let Some((next_tick, _)) = bitmap_utils::next_initialized_tick_within_one_word(
            bitmap[word_index],
            current_tick,
            tick_spacing,
            !zero_for_one,
        ) {
            return Some(next_tick);
        }

        // 在相邻字中查找
        let search_range = if zero_for_one {
            (0..word_index).rev().collect::<Vec<_>>()
        } else {
            ((word_index + 1)..16).collect::<Vec<_>>()
        };

        for &idx in &search_range {
            if bitmap[idx] != 0 {
                let base_tick = (idx as i32 - 8) * 256 * tick_spacing;
                if let Some((next_tick, _)) = bitmap_utils::next_initialized_tick_within_one_word(
                    bitmap[idx],
                    base_tick,
                    tick_spacing,
                    !zero_for_one,
                ) {
                    return Some(next_tick);
                }
            }
        }

        None
    }

    /// 在扩展位图中搜索
    fn search_in_extended_bitmap(
        &self,
        current_tick: i32,
        zero_for_one: bool,
        tick_spacing: i32,
        bitmap_ext: &TickArrayBitmapExtension,
    ) -> Option<i32> {
        let bitmap_array = if current_tick >= 0 {
            &bitmap_ext.positive_tick_array_bitmap
        } else {
            &bitmap_ext.negative_tick_array_bitmap
        };

        // 简化的扩展位图搜索实现
        for (i, row) in bitmap_array.iter().enumerate() {
            for (j, &word) in row.iter().enumerate() {
                if word != 0 {
                    let base_tick = (i as i32 * 8 + j as i32) * 256 * tick_spacing;
                    let base_tick = if current_tick < 0 { -base_tick } else { base_tick };

                    if let Some((next_tick, _)) = bitmap_utils::next_initialized_tick_within_one_word(
                        word,
                        base_tick,
                        tick_spacing,
                        !zero_for_one,
                    ) {
                        if (zero_for_one && next_tick < current_tick) ||
                           (!zero_for_one && next_tick > current_tick) {
                            return Some(next_tick);
                        }
                    }
                }
            }
        }

        None
    }

    /// 线性搜索下一个初始化的tick (回退方法)
    fn find_next_tick_linear(
        &self,
        current_tick: i32,
        zero_for_one: bool,
    ) -> Result<Option<i32>> {
        let _tick_spacing = self.pool_state.tick_spacing as i32;
        let mut best_tick: Option<i32> = None;

        for tick_array in self.tick_arrays.values() {
            for tick_info in &tick_array.ticks {
                if tick_info.liquidity_gross > 0 {
                    let is_valid = if zero_for_one {
                        tick_info.tick < current_tick
                    } else {
                        tick_info.tick > current_tick
                    };

                    if is_valid {
                        match best_tick {
                            None => best_tick = Some(tick_info.tick),
                            Some(current_best) => {
                                if zero_for_one && tick_info.tick > current_best {
                                    best_tick = Some(tick_info.tick);
                                } else if !zero_for_one && tick_info.tick < current_best {
                                    best_tick = Some(tick_info.tick);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(best_tick)
    }

    /// 计算价格影响 (以基点为单位)
    fn calculate_price_impact(
        &self,
        initial_sqrt_price: u128,
        final_sqrt_price: u128,
    ) -> u32 {
        if initial_sqrt_price == 0 {
            return 0;
        }

        let price_change = if final_sqrt_price > initial_sqrt_price {
            final_sqrt_price - initial_sqrt_price
        } else {
            initial_sqrt_price - final_sqrt_price
        };

        // 计算百分比变化并转换为基点
        let impact_ratio = (price_change as f64) / (initial_sqrt_price as f64);
        (impact_ratio * 10000.0) as u32
    }

    /// 获取池的流动性分布信息
    pub fn get_liquidity_distribution(&self) -> Vec<(i32, u128)> {
        let mut distribution = Vec::new();

        for tick_array in self.tick_arrays.values() {
            for tick_info in &tick_array.ticks {
                if tick_info.liquidity_gross > 0 {
                    distribution.push((tick_info.tick, tick_info.liquidity_gross));
                }
            }
        }

        distribution.sort_by_key(|&(tick, _)| tick);
        distribution
    }

    /// 获取池统计信息
    pub fn get_pool_stats(&self) -> PoolStats {
        let total_ticks = self.tick_arrays.values()
            .map(|array| array.ticks.len())
            .sum();

        let initialized_ticks = self.tick_arrays.values()
            .map(|array| array.ticks.iter().filter(|t| t.liquidity_gross > 0).count())
            .sum();

        PoolStats {
            total_liquidity: self.pool_state.liquidity,
            current_tick: self.pool_state.tick_current,
            current_price: self.get_current_price(),
            total_ticks,
            initialized_ticks,
            tick_arrays_count: self.tick_arrays.len(),
            tvl_token_0: 0.0, // 需要根据价格计算
            tvl_token_1: 0.0, // 需要根据价格计算
        }
    }
}

